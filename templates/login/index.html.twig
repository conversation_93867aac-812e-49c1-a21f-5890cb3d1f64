{% extends 'login-base.html.twig' %}

{% block content %}
<div id="login-container" class="ui grid">
    <div id="login-box" class="ui column {{ view.envClass }}">
        <div class="ui raised segment left aligned">
            {% if app.environment != 'prod' %}
                {% set env_string = app.environment %}
                {% if env_string == 'dev' %}
                    {% set color = 'purple' %}
                {% elseif env_string == 'staging' %}
                    {% set color = 'orange' %}
                {% else %}
                    {% set color = 'teal' %}
                {% endif %}
                <a class="ui {{ color }} ribbon label">{{ env_string }}</a>
            {% endif %}

            <form class="ui form" method="post" action="{{ path('login_check') }}">
                <img src="{{ asset('images/mysparefootx2.png') }}" alt="MySpareFoot" class="mysparefootx2" />
                {% if view.error %}
                <p class="ui message negative">{{ view.error }}</p>
                {% endif %}
                <div class="field">
                    <input name="_username" id="email" type="text" placeholder="Email" value="{{ view.last_username }}"/>
                </div>
                <div class="field">
                    <input name="_password" id="password" type="password" placeholder="Password"/>
                </div>
                <div class="two fields">
                    <div class="field remember-me-field">
                        <input name="remember" type="hidden" />
                        <div class="ui checkbox">
                            <input name="_remember_me" type="checkbox" {% if view.remember is defined and view.remember %}checked="checked"{% endif %}/>
                            <label>Stay signed in</label>
                        </div>
                    </div>
                    <div class="field">
                        <a id="forgot-password" href="#password-reset-modal">Forgot your password?</a>
                    </div>
                </div>
                <input name="login_buttond" type="hidden" value="Log In"/>
                <button id="login-button" class="ui submit button fluid primary large login-button">Sign In</button>
                <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
            </form>
            <div class="ui divider"></div>
            <p class="centered column signup-row">
                New to SpareFoot? <a id="signup-button" href="/signup-start">Sign Up</a>
            </p>
        </div>
    </div>
</div>

<form id="password-reset-modal" class="ui modal small">
    <i class="close icon" id="password-reset-modal-close"></i>
    <div class="header">Forgot Your Password?</div>
    <div class="content">
        <div class="ui form">
            <p>Enter your email address and we'll send you directions to reset your password.</p>
            <div class="field fluid">
                <input type="text" id="reset-email" name="email" value="{{ view.last_username }}" placeholder="Email address" />
            </div>
            <div class="ui message"></div>
        </div>
    </div>
    <div class="actions">
        <button class="ui button default close">Cancel</button>
        <button class="ui button primary" type="submit">Submit</button>
    </div>
</form>
{% endblock %}
